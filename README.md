# FastAPI Best Architecture UI

Front-end Implementation of the [FastAPI Best Architecture](https://github.com/fastapi-practices/fastapi_best_architecture)

## Run

```shell
pnpm install
pnpm dev
```

## Build

```shell
pnpm build
```

## Contributors

<a href="https://github.com/fastapi-practices/fba_ui/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=fastapi-practices/fba_ui"/>
</a>

## Special thanks

- [Vue.js](https://cn.vuejs.org/guide/introduction.html)
- [Vben Admin](https://www.vben.pro/)
- ...

## Sponsor us

If this program has helped you, you can sponsor us with some coffee beans: [:coffee: Sponsor :coffee:](https://wu-clan.github.io/sponsor/)

## License

This project is licensed under the terms of the [MIT](https://github.com/fastapi-practices/fba_ui/blob/master/LICENSE) license
