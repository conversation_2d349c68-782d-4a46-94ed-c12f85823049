worker_processes auto;
pid /run/nginx.pid;
error_log /var/log/nginx/error.log;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 8192;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    sendfile on;
    client_max_body_size 5M;
    client_body_buffer_size 5M;

    gzip on;
    gzip_comp_level 2;
    gzip_types text/plain text/css text/javascript application/javascript application/x-javascript application/xml application/x-httpd-php image/jpeg image/gif image/png;
    gzip_vary on;

    keepalive_timeout 300;

    access_log /var/log/nginx/access.log;

    server {
        listen 80;
        listen [::]:80;
    #    server_name xxx.com;
    #    rewrite ^(.*)$ https://$host$1 permanent;
    }

    server {
        server_name  127.0.0.1;

        # listen 443 ssl;
        # ssl_certificate /etc/ssl/xxx.pem;
        # ssl_certificate_key /etc/ssl/xxx.key;
        # ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
        # ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        # ssl_prefer_server_ciphers on;

        client_max_body_size   5m;

        root /var/www/fba_ui;

        location / {
            try_files $uri $uri/ /index.html;
        }

        location /api/v1/ {
            proxy_pass http://fba_server:8001;

            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }

        location /ws/socket.io/ {
            proxy_pass http://fba_server:8001/ws/socket.io/;

            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 86400s;
            proxy_send_timeout 86400s;

            # WebSocket 支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_buffering off;
        }

        location /flower/ {
            proxy_pass http://fba_server:8555;

            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            proxy_redirect off;

            # WebSocket 支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        location /static/ {
            alias /var/www/fba_server/backend/static;
        }

        location /static/upload/ {
            alias /var/www/fba_server/backend/static/upload;
        }

        error_page 500 502 503 504 /50x.html;

        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}