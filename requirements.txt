# This file was autogenerated by uv via the following command:
#    uv export -o requirements.txt --no-hashes
-e .
aiofiles==24.1.0
    # via fastapi-best-architecture
alembic==1.16.4
    # via fastapi-best-architecture
amqp==5.3.1
    # via kombu
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   starlette
    #   watchfiles
asgi-correlation-id==4.3.4
    # via fastapi-best-architecture
asgiref==3.9.1
    # via fastapi-best-architecture
async-timeout==5.0.1 ; python_full_version < '3.11.3'
    # via
    #   asyncpg
    #   redis
asyncmy==0.2.10
    # via fastapi-best-architecture
asyncpg==0.30.0
    # via fastapi-best-architecture
bcrypt==4.3.0
    # via fastapi-best-architecture
bidict==0.23.1
    # via python-socketio
billiard==4.2.1
    # via celery
cappa==0.28.1
    # via fastapi-best-architecture
celery==5.5.3
    # via
    #   celery-aio-pool
    #   fastapi-best-architecture
    #   flower
celery-aio-pool==0.1.0rc8
    # via fastapi-best-architecture
certifi==2025.7.14
    # via
    #   httpcore
    #   httpx
cffi==1.17.1 ; platform_python_implementation != 'PyPy'
    # via
    #   cryptography
    #   gevent
cfgv==3.4.0
    # via pre-commit
click==8.2.1
    # via
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
    #   granian
    #   rich-toolkit
    #   typer
    #   uvicorn
click-didyoumean==0.3.1
    # via celery
click-plugins==1.1.1.2
    # via celery
click-repl==0.3.0
    # via celery
colorama==0.4.6 ; sys_platform == 'win32'
    # via
    #   click
    #   loguru
    #   pytest
    #   uvicorn
cryptography==45.0.5
    # via fastapi-best-architecture
distlib==0.3.9
    # via virtualenv
dnspython==2.7.0
    # via email-validator
dulwich==0.23.2
    # via fastapi-best-architecture
ecdsa==0.19.1
    # via python-jose
email-validator==2.2.0
    # via fastapi
exceptiongroup==1.3.0 ; python_full_version < '3.11'
    # via
    #   anyio
    #   pytest
fast-captcha==0.3.2
    # via fastapi-best-architecture
fastapi==0.116.1
    # via
    #   fastapi-best-architecture
    #   fastapi-limiter
    #   fastapi-pagination
fastapi-cli==0.0.8
    # via fastapi
fastapi-limiter==0.1.6
    # via fastapi-best-architecture
fastapi-pagination==0.13.3
    # via fastapi-best-architecture
filelock==3.18.0
    # via virtualenv
flower==2.0.1
    # via fastapi-best-architecture
gevent==25.5.1
    # via fastapi-best-architecture
granian==2.4.2
    # via fastapi-best-architecture
greenlet==3.2.3
    # via
    #   gevent
    #   sqlalchemy
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
    #   wsproto
hiredis==3.2.1
    # via redis
httpcore==1.0.9
    # via httpx
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via fastapi
humanize==4.12.3
    # via flower
identify==2.6.12
    # via pre-commit
idna==3.10
    # via
    #   anyio
    #   email-validator
    #   httpx
iniconfig==2.1.0
    # via pytest
ip2loc==1.0.0
    # via fastapi-best-architecture
itsdangerous==2.2.0
    # via fastapi-best-architecture
jinja2==3.1.6
    # via
    #   fastapi
    #   fastapi-best-architecture
kombu==5.5.4
    # via celery
loguru==0.7.3
    # via fastapi-best-architecture
mako==1.3.10
    # via alembic
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via
    #   jinja2
    #   mako
mdurl==0.1.2
    # via markdown-it-py
msgspec==0.19.0
    # via fastapi-best-architecture
nodeenv==1.9.1
    # via pre-commit
packaging==25.0
    # via
    #   asgi-correlation-id
    #   kombu
    #   pytest
    #   pytest-sugar
path==17.1.0
    # via fastapi-best-architecture
pillow==11.3.0
    # via fast-captcha
platformdirs==4.3.8
    # via virtualenv
pluggy==1.6.0
    # via pytest
pre-commit==4.2.0
prometheus-client==0.22.1
    # via flower
prompt-toolkit==3.0.51
    # via click-repl
psutil==7.0.0
    # via fastapi-best-architecture
psycopg==3.2.9
    # via fastapi-best-architecture
pwdlib==0.2.1
    # via fastapi-best-architecture
pyasn1==0.6.1
    # via
    #   python-jose
    #   rsa
pycparser==2.22 ; platform_python_implementation != 'PyPy'
    # via cffi
pydantic==2.11.7
    # via
    #   fastapi
    #   fastapi-best-architecture
    #   fastapi-pagination
    #   pydantic-settings
    #   sqlalchemy-crud-plus
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via fastapi-best-architecture
pygments==2.19.2
    # via
    #   pytest
    #   rich
pymysql==1.1.1
    # via fastapi-best-architecture
pytest==8.4.1
    # via pytest-sugar
pytest-sugar==1.0.0
python-dateutil==2.9.0.post0
    # via celery
python-dotenv==1.1.1
    # via
    #   pydantic-settings
    #   uvicorn
python-engineio==4.12.2
    # via python-socketio
python-jose==3.5.0
    # via fastapi-best-architecture
python-multipart==0.0.20
    # via fastapi
python-socketio==5.13.0
    # via fastapi-best-architecture
pytz==2025.2
    # via flower
pyyaml==6.0.2
    # via
    #   pre-commit
    #   uvicorn
redis==6.2.0
    # via
    #   fastapi-best-architecture
    #   fastapi-limiter
rich==14.0.0
    # via
    #   cappa
    #   rich-toolkit
    #   typer
rich-toolkit==0.14.8
    # via fastapi-cli
rsa==4.9.1
    # via python-jose
rtoml==0.12.0
    # via fastapi-best-architecture
setuptools==80.9.0
    # via
    #   zope-event
    #   zope-interface
shellingham==1.5.4
    # via typer
simple-websocket==1.1.0
    # via python-engineio
six==1.17.0
    # via
    #   ecdsa
    #   python-dateutil
sniffio==1.3.1
    # via anyio
sqlalchemy==2.0.41
    # via
    #   alembic
    #   fastapi-best-architecture
    #   sqlalchemy-crud-plus
sqlalchemy-crud-plus==1.10.0
    # via fastapi-best-architecture
sqlparse==0.5.3
    # via fastapi-best-architecture
starlette==0.47.1
    # via
    #   asgi-correlation-id
    #   fastapi
termcolor==3.1.0
    # via pytest-sugar
tomli==2.2.1 ; python_full_version < '3.11'
    # via
    #   alembic
    #   pytest
tornado==6.5.1
    # via flower
type-lens==0.2.3
    # via cappa
typer==0.16.0
    # via fastapi-cli
typing-extensions==4.14.1
    # via
    #   alembic
    #   anyio
    #   asgiref
    #   cappa
    #   exceptiongroup
    #   fastapi
    #   fastapi-pagination
    #   psycopg
    #   pydantic
    #   pydantic-core
    #   rich
    #   rich-toolkit
    #   sqlalchemy
    #   starlette
    #   type-lens
    #   typer
    #   typing-inspection
    #   uvicorn
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
tzdata==2025.2
    # via
    #   kombu
    #   psycopg
ua-parser==1.0.1
    # via user-agents
ua-parser-builtins==0.18.0.post1
    # via ua-parser
urllib3==2.5.0
    # via dulwich
user-agents==2.2.0
    # via fastapi-best-architecture
uvicorn==0.35.0
    # via
    #   fastapi
    #   fastapi-cli
uvloop==0.21.0 ; platform_python_implementation != 'PyPy' and sys_platform != 'cygwin' and sys_platform != 'win32'
    # via uvicorn
vine==5.1.0
    # via
    #   amqp
    #   celery
    #   kombu
virtualenv==20.31.2
    # via pre-commit
watchfiles==1.1.0
    # via uvicorn
wcwidth==0.2.13
    # via prompt-toolkit
websockets==15.0.1
    # via uvicorn
win32-setctime==1.2.0 ; sys_platform == 'win32'
    # via loguru
wsproto==1.2.0
    # via simple-websocket
zope-event==5.1
    # via gevent
zope-interface==7.2
    # via gevent
