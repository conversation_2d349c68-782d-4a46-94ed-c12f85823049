line-length = 120
cache-dir = ".ruff_cache"
target-version = "py310"
unsafe-fixes = true
show-fixes = true

[lint]
select = [
    "E",
    "F",
    "I",
    "TC",
    # W
    "W505",
    # PT
    "PT018",
    # SIM
    "SIM101",
    "SIM114",
    # PGH
    "PGH004",
    # PL
    "PLE1142",
    # RUF
    "RUF100",
    # UP
    "UP007"
]
preview = true
ignore = ["FURB101"]

[lint.flake8-pytest-style]
mark-parentheses = false
parametrize-names-type = "list"
parametrize-values-row-type = "list"
parametrize-values-type = "tuple"

[lint.flake8-unused-arguments]
ignore-variadic-names = true

[lint.isort]
lines-between-types = 1
order-by-type = true

[lint.per-file-ignores]
"**/api/v1/*.py" = ["TC"]
"**/model/*.py" = ["TC003"]
"**/model/__init__.py" = ["F401"]
"**/tests/*.py" = ["E402"]

[format]
preview = true
quote-style = "single"
docstring-code-format = true
skip-magic-trailing-comma = false
