repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
#      - id: check-added-large-files
#      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-toml

  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.11.9
    hooks:
      - id: ruff
        args:
          # https://github.com/astral-sh/ruff-pre-commit/issues/64
          - '--config'
          - 'backend/.ruff.toml'
          - '--fix'
          - '--unsafe-fixes'
      - id: ruff-format

  - repo: https://github.com/astral-sh/uv-pre-commit
    rev: 0.7.3
    hooks:
      - id: uv-lock
      - id: uv-export
        args:
          - '-o'
          - 'requirements.txt'
          - '--no-hashes'
