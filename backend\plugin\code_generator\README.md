> [!TIP]
> 当前版本仅包含后端代码生成

> [!WARNING]
> 由于 jinja2 在渲染模版时，文本方式输出可能存在格式问题，所以 `preview` 接口可能无法直观预览代码，这是为前端进行的预设

## 简介

代码生成器使用 api 调用实现，包含两个模组，设计可能存在缺陷，相关问题请直接提交 issues

### 1. 代码生成业务

包含代码生成的相关配置，详情查看：`generator/model/gen_business.py`

### 2. 代码生成模型

包含代码生成所需要的模型列信息，就像正常定义模型列一样，目前支持的功能有限

## 使（食）用

1. 启动后端服务，打开 swagger 文档直接操作
2. 通过第三方 api 调试工具发送接口请求
3. 同时启动前后端，从页面进行操作

接口参数基本都有说明，请注意查看

### F. 纯手动模式

不推荐（手动创建业务接口被标记为「已弃用」）

1. 通过创建业务接口手动添加一项业务数据
2. 通过模型创建接口手动添加模型列
3. 访问 `preview`（预览），`generate`（磁盘写入），`download`（下载）接口，执行后端代码生成相应工作

### S. 自动模式

推荐

1. 访问 `tables` 接口，获取数据库表名列表
2. 通过 `import` 接口，导入数据库已有的数据库表数据，将自动创建业务表数据和模型表数据
3. 访问 `preview`（预览），`generate`（磁盘写入），`download`（下载）接口，执行后端代码生成相应工作
